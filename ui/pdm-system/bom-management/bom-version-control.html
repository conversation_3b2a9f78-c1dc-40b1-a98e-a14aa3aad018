<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BOM版本管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="bomVersionApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">BOM版本管理</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / BOM版本管理</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="showCompareModal = true" :disabled="selectedVersions.length !== 2"
                        class="px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600 transition-colors disabled:bg-disabled-gray">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2h2a2 2 0 002-2z"/>
                    </svg>
                    版本对比
                </button>
                <button @click="createNewVersion" :disabled="!canCreateVersion"
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors disabled:bg-disabled-gray">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    创建新版本
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-16">
        <!-- 左侧BOM列表区域 -->
        <div class="w-80 bg-white border-r border-border-gray">
            <div class="p-4">
                <!-- 搜索框 -->
                <div class="relative mb-4">
                    <input type="text" x-model="bomSearch" @input="filterBoms"
                           placeholder="搜索BOM名称或编码..."
                           class="w-full pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    <svg class="absolute left-2 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>

                <!-- 筛选器 -->
                <div class="mb-4">
                    <select x-model="statusFilter" @change="filterBoms"
                            class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">全部状态</option>
                        <option value="草稿">草稿</option>
                        <option value="激活">激活</option>
                        <option value="归档">归档</option>
                        <option value="锁定">锁定</option>
                    </select>
                </div>

                <!-- BOM列表 -->
                <div class="space-y-2">
                    <h3 class="text-sm font-medium text-title-gray mb-3">BOM列表</h3>
                    <template x-for="bom in filteredBoms" :key="bom.id">
                        <div class="p-3 border border-border-gray rounded-md cursor-pointer hover:border-primary"
                             :class="selectedBom?.id === bom.id ? 'border-primary bg-blue-50' : ''"
                             @click="selectBom(bom)">
                            <div class="flex items-center justify-between mb-2">
                                <div class="text-sm font-medium text-title-gray" x-text="bom.name"></div>
                                <span class="px-2 py-1 text-xs rounded-full"
                                      :class="getBomStatusClass(bom.currentStatus)"
                                      x-text="bom.currentStatus"></span>
                            </div>
                            <div class="text-xs text-aux-gray" x-text="bom.code"></div>
                            <div class="text-xs text-aux-gray" x-text="`当前版本: ${bom.currentVersion}`"></div>
                            <div class="text-xs text-aux-gray" x-text="`共 ${bom.versionCount} 个版本`"></div>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 中间版本列表区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 版本列表头部 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <template x-if="selectedBom">
                    <div class="flex items-center justify-between">
                        <div>
                            <h2 class="text-lg font-medium text-title-gray" x-text="selectedBom.name"></h2>
                            <p class="text-sm text-aux-gray" x-text="`BOM编码: ${selectedBom.code}`"></p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-aux-gray">选择版本进行对比:</span>
                            <span class="text-xs text-aux-gray" x-text="`已选择 ${selectedVersions.length}/2`"></span>
                        </div>
                    </div>
                </template>
                <template x-if="!selectedBom">
                    <div class="text-center text-aux-gray">
                        <p class="text-sm">请从左侧选择一个BOM查看版本信息</p>
                    </div>
                </template>
            </div>

            <!-- 版本列表内容 -->
            <div class="flex-1 overflow-auto bg-white">
                <template x-if="selectedBom">
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <template x-for="version in selectedBom.versions" :key="version.id">
                                <div class="border border-border-gray rounded-lg p-4 hover:border-primary transition-colors"
                                     :class="selectedVersions.includes(version.id) ? 'border-primary bg-blue-50' : ''"
                                     @click="toggleVersionSelection(version)">
                                    <!-- 版本头部 -->
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center space-x-2">
                                            <h3 class="text-lg font-medium text-title-gray" x-text="version.version"></h3>
                                            <span class="px-2 py-1 text-xs rounded-full"
                                                  :class="getVersionStatusClass(version.status)"
                                                  x-text="version.status"></span>
                                        </div>
                                        <template x-if="selectedVersions.includes(version.id)">
                                            <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                        </template>
                                    </div>

                                    <!-- 版本信息 -->
                                    <div class="space-y-2 mb-4">
                                        <div class="flex justify-between text-sm">
                                            <span class="text-aux-gray">创建时间:</span>
                                            <span class="text-body-gray" x-text="version.createTime"></span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-aux-gray">创建人:</span>
                                            <span class="text-body-gray" x-text="version.creator"></span>
                                        </div>
                                        <div class="flex justify-between text-sm">
                                            <span class="text-aux-gray">物料数量:</span>
                                            <span class="text-body-gray" x-text="`${version.materialCount} 项`"></span>
                                        </div>
                                    </div>

                                    <!-- 变更说明 -->
                                    <div class="mb-4">
                                        <p class="text-xs text-aux-gray mb-1">变更说明:</p>
                                        <p class="text-sm text-body-gray" x-text="version.changeDescription || '无'"></p>
                                    </div>

                                    <!-- 操作按钮 -->
                                    <div class="flex space-x-2">
                                        <button @click.stop="viewVersion(version)" 
                                                class="flex-1 px-3 py-2 text-sm bg-gray-100 text-body-gray rounded-md hover:bg-gray-200">
                                            查看详情
                                        </button>
                                        <template x-if="version.status === '草稿'">
                                            <button @click.stop="editVersion(version)" 
                                                    class="flex-1 px-3 py-2 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                                                编辑
                                            </button>
                                        </template>
                                        <template x-if="version.status === '草稿' && canApprove">
                                            <button @click.stop="submitForApproval(version)" 
                                                    class="flex-1 px-3 py-2 text-sm bg-success text-white rounded-md hover:bg-green-600">
                                                提交审批
                                            </button>
                                        </template>
                                    </div>

                                    <!-- 审批信息 -->
                                    <template x-if="version.approvalInfo">
                                        <div class="mt-3 pt-3 border-t border-border-gray">
                                            <div class="text-xs text-aux-gray mb-1">审批信息:</div>
                                            <div class="text-xs text-body-gray">
                                                <div>审批人: <span x-text="version.approvalInfo.approver"></span></div>
                                                <div>审批时间: <span x-text="version.approvalInfo.approvalTime"></span></div>
                                                <div x-show="version.approvalInfo.comment">
                                                    意见: <span x-text="version.approvalInfo.comment"></span>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>

                <!-- 未选择BOM时的提示 -->
                <template x-if="!selectedBom">
                    <div class="flex items-center justify-center h-full">
                        <div class="text-center text-aux-gray">
                            <svg class="w-16 h-16 mx-auto mb-4 text-disabled-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            <p class="text-lg">请选择BOM查看版本信息</p>
                            <p class="text-sm mt-2">从左侧列表中选择一个BOM开始管理版本</p>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- 右侧详情面板 -->
        <div class="w-96 bg-white border-l border-border-gray" x-show="selectedVersion">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-title-gray">版本详情</h3>
                    <button @click="selectedVersion = null" class="text-aux-gray hover:text-title-gray">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <template x-if="selectedVersion">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">基本信息</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">版本号</label>
                                    <div class="text-sm text-body-gray" x-text="selectedVersion.version"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">状态</label>
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getVersionStatusClass(selectedVersion.status)"
                                          x-text="selectedVersion.status"></span>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">创建时间</label>
                                    <div class="text-sm text-body-gray" x-text="selectedVersion.createTime"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">创建人</label>
                                    <div class="text-sm text-body-gray" x-text="selectedVersion.creator"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 变更说明 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">变更说明</h4>
                            <div class="text-sm text-body-gray" x-text="selectedVersion.changeDescription || '无变更说明'"></div>
                        </div>

                        <!-- 物料统计 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">物料统计</h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">物料总数</span>
                                    <span class="text-sm font-medium text-title-gray" x-text="selectedVersion.materialCount"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">原材料</span>
                                    <span class="text-sm text-body-gray" x-text="selectedVersion.rawMaterialCount || 0"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">半成品</span>
                                    <span class="text-sm text-body-gray" x-text="selectedVersion.semiProductCount || 0"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">辅料</span>
                                    <span class="text-sm text-body-gray" x-text="selectedVersion.auxiliaryCount || 0"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 审批信息 -->
                        <template x-if="selectedVersion.approvalInfo">
                            <div>
                                <h4 class="text-sm font-medium text-title-gray mb-3">审批信息</h4>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-body-gray">审批人</span>
                                        <span class="text-sm text-body-gray" x-text="selectedVersion.approvalInfo.approver"></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-body-gray">审批时间</span>
                                        <span class="text-sm text-body-gray" x-text="selectedVersion.approvalInfo.approvalTime"></span>
                                    </div>
                                    <template x-if="selectedVersion.approvalInfo.comment">
                                        <div>
                                            <label class="block text-xs text-aux-gray mb-1">审批意见</label>
                                            <div class="text-sm text-body-gray" x-text="selectedVersion.approvalInfo.comment"></div>
                                        </div>
                                    </template>
                                </div>
                            </div>
                        </template>

                        <!-- 操作按钮 -->
                        <div class="space-y-2">
                            <button @click="viewVersionDetail(selectedVersion)" 
                                    class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600">
                                查看完整BOM
                            </button>
                            <template x-if="selectedVersion.status === '草稿'">
                                <button @click="editVersion(selectedVersion)" 
                                        class="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                                    编辑版本
                                </button>
                            </template>
                            <template x-if="selectedVersion.status === '激活'">
                                <button @click="archiveVersion(selectedVersion)" 
                                        class="w-full px-4 py-2 bg-warning text-white rounded-md hover:bg-yellow-600">
                                    归档版本
                                </button>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </main>

    <script>
        function bomVersionApp() {
            return {
                // UI状态
                selectedBom: null,
                selectedVersion: null,
                selectedVersions: [],
                showCompareModal: false,
                bomSearch: '',
                statusFilter: '',
                canApprove: true, // 模拟用户权限

                // 数据
                boms: [
                    {
                        id: 'BOM001',
                        name: '钢化玻璃BOM',
                        code: 'BOM-TG-001',
                        currentVersion: 'V2.1',
                        currentStatus: '激活',
                        versionCount: 5,
                        versions: [
                            {
                                id: 'V001',
                                version: 'V2.1',
                                status: '激活',
                                createTime: '2025-07-30 14:30',
                                creator: '张工程师',
                                materialCount: 8,
                                rawMaterialCount: 5,
                                semiProductCount: 2,
                                auxiliaryCount: 1,
                                changeDescription: '优化玻璃原片用量计算公式，减少5%损耗',
                                approvalInfo: {
                                    approver: '李主管',
                                    approvalTime: '2025-07-30 16:45',
                                    comment: '审批通过，公式优化合理'
                                }
                            },
                            {
                                id: 'V002',
                                version: 'V2.0',
                                status: '归档',
                                createTime: '2025-07-25 10:15',
                                creator: '张工程师',
                                materialCount: 8,
                                rawMaterialCount: 5,
                                semiProductCount: 2,
                                auxiliaryCount: 1,
                                changeDescription: '增加新的胶水辅料，提升产品质量',
                                approvalInfo: {
                                    approver: '李主管',
                                    approvalTime: '2025-07-25 15:20',
                                    comment: '审批通过'
                                }
                            },
                            {
                                id: 'V003',
                                version: 'V1.2',
                                status: '草稿',
                                createTime: '2025-07-31 09:00',
                                creator: '王工程师',
                                materialCount: 9,
                                rawMaterialCount: 6,
                                semiProductCount: 2,
                                auxiliaryCount: 1,
                                changeDescription: '测试新的包装材料方案'
                            }
                        ]
                    },
                    {
                        id: 'BOM002',
                        name: '夹胶玻璃BOM',
                        code: 'BOM-LG-001',
                        currentVersion: 'V1.5',
                        currentStatus: '激活',
                        versionCount: 3,
                        versions: [
                            {
                                id: 'V004',
                                version: 'V1.5',
                                status: '激活',
                                createTime: '2025-07-28 11:20',
                                creator: '赵工程师',
                                materialCount: 6,
                                rawMaterialCount: 4,
                                semiProductCount: 1,
                                auxiliaryCount: 1,
                                changeDescription: '调整PVB胶片厚度规格',
                                approvalInfo: {
                                    approver: '李主管',
                                    approvalTime: '2025-07-28 14:30',
                                    comment: '审批通过'
                                }
                            },
                            {
                                id: 'V005',
                                version: 'V1.4',
                                status: '锁定',
                                createTime: '2025-07-20 16:45',
                                creator: '赵工程师',
                                materialCount: 6,
                                rawMaterialCount: 4,
                                semiProductCount: 1,
                                auxiliaryCount: 1,
                                changeDescription: '初始版本',
                                approvalInfo: {
                                    approver: '李主管',
                                    approvalTime: '2025-07-20 18:00',
                                    comment: '审批通过'
                                }
                            }
                        ]
                    }
                ],

                filteredBoms: [],

                init() {
                    this.filteredBoms = [...this.boms];
                },

                get canCreateVersion() {
                    if (!this.selectedBom) return false;
                    // 检查是否有草稿状态的版本
                    return !this.selectedBom.versions.some(v => v.status === '草稿');
                },

                // 筛选方法
                filterBoms() {
                    let filtered = [...this.boms];

                    if (this.bomSearch) {
                        const query = this.bomSearch.toLowerCase();
                        filtered = filtered.filter(bom =>
                            bom.name.toLowerCase().includes(query) ||
                            bom.code.toLowerCase().includes(query)
                        );
                    }

                    if (this.statusFilter) {
                        filtered = filtered.filter(bom => bom.currentStatus === this.statusFilter);
                    }

                    this.filteredBoms = filtered;
                },

                // BOM选择
                selectBom(bom) {
                    this.selectedBom = bom;
                    this.selectedVersion = null;
                    this.selectedVersions = [];
                },

                // 版本选择
                toggleVersionSelection(version) {
                    const index = this.selectedVersions.indexOf(version.id);
                    if (index > -1) {
                        this.selectedVersions.splice(index, 1);
                    } else {
                        if (this.selectedVersions.length < 2) {
                            this.selectedVersions.push(version.id);
                        } else {
                            // 替换第一个选择
                            this.selectedVersions[0] = this.selectedVersions[1];
                            this.selectedVersions[1] = version.id;
                        }
                    }
                    this.selectedVersion = version;
                },

                // 状态样式
                getBomStatusClass(status) {
                    const classMap = {
                        '草稿': 'bg-yellow-100 text-yellow-800',
                        '激活': 'bg-green-100 text-green-800',
                        '归档': 'bg-gray-100 text-gray-800',
                        '锁定': 'bg-red-100 text-red-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },

                getVersionStatusClass(status) {
                    const classMap = {
                        '草稿': 'bg-yellow-100 text-yellow-800',
                        '激活': 'bg-green-100 text-green-800',
                        '归档': 'bg-gray-100 text-gray-800',
                        '锁定': 'bg-red-100 text-red-800',
                        '审批中': 'bg-blue-100 text-blue-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },

                // 版本操作
                createNewVersion() {
                    if (!this.selectedBom) {
                        alert('请先选择一个BOM');
                        return;
                    }

                    if (!this.canCreateVersion) {
                        alert('当前BOM存在草稿版本，请先处理后再创建新版本');
                        return;
                    }

                    const changeDescription = prompt('请输入版本变更说明:');
                    if (changeDescription === null) return;

                    // 生成新版本号
                    const currentVersion = this.selectedBom.currentVersion;
                    const versionParts = currentVersion.replace('V', '').split('.');
                    const newMinorVersion = parseInt(versionParts[1]) + 1;
                    const newVersion = `V${versionParts[0]}.${newMinorVersion}`;

                    const newVersionObj = {
                        id: 'V' + Date.now(),
                        version: newVersion,
                        status: '草稿',
                        createTime: new Date().toLocaleString('zh-CN'),
                        creator: '当前用户',
                        materialCount: 8,
                        rawMaterialCount: 5,
                        semiProductCount: 2,
                        auxiliaryCount: 1,
                        changeDescription: changeDescription
                    };

                    this.selectedBom.versions.unshift(newVersionObj);
                    this.selectedBom.versionCount++;

                    alert(`新版本 ${newVersion} 创建成功！`);
                },

                viewVersion(version) {
                    this.selectedVersion = version;
                },

                editVersion(version) {
                    alert(`编辑版本: ${version.version}`);
                },

                submitForApproval(version) {
                    if (confirm(`确定提交版本 ${version.version} 进行审批吗？`)) {
                        version.status = '审批中';
                        alert('已提交审批，等待审批人员处理');
                    }
                },

                viewVersionDetail(version) {
                    alert(`查看版本 ${version.version} 的完整BOM结构`);
                },

                archiveVersion(version) {
                    if (confirm(`确定归档版本 ${version.version} 吗？`)) {
                        version.status = '归档';
                        alert('版本已归档');
                    }
                }
            }
        }
    </script>
</body>
</html>
