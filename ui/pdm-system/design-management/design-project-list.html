<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品设计管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="designApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">产品设计管理</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / 产品设计管理</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="showCreateModal = true" 
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    新建设计项目
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-16">
        <!-- 左侧筛选区域 -->
        <div class="w-64 bg-white border-r border-border-gray">
            <div class="p-4">
                <h3 class="text-sm font-medium text-title-gray mb-3">筛选条件</h3>
                
                <!-- 项目状态筛选 -->
                <div class="mb-4">
                    <label class="block text-xs font-medium text-aux-gray mb-2">项目状态</label>
                    <div class="space-y-1">
                        <label class="flex items-center">
                            <input type="checkbox" x-model="statusFilters" value="designing" class="mr-2">
                            <span class="text-sm text-body-gray">设计中</span>
                            <span class="text-xs text-aux-gray ml-auto">(5)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="statusFilters" value="reviewing" class="mr-2">
                            <span class="text-sm text-body-gray">评审中</span>
                            <span class="text-xs text-aux-gray ml-auto">(3)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="statusFilters" value="completed" class="mr-2">
                            <span class="text-sm text-body-gray">已完成</span>
                            <span class="text-xs text-aux-gray ml-auto">(12)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="statusFilters" value="paused" class="mr-2">
                            <span class="text-sm text-body-gray">已暂停</span>
                            <span class="text-xs text-aux-gray ml-auto">(2)</span>
                        </label>
                    </div>
                </div>

                <!-- 产品类型筛选 -->
                <div class="mb-4">
                    <label class="block text-xs font-medium text-aux-gray mb-2">产品类型</label>
                    <select x-model="productTypeFilter" @change="filterProjects" 
                            class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">全部类型</option>
                        <option value="building">建筑玻璃</option>
                        <option value="furniture">家具玻璃</option>
                        <option value="decoration">装饰玻璃</option>
                        <option value="special">特种玻璃</option>
                    </select>
                </div>

                <!-- 责任人筛选 -->
                <div class="mb-4">
                    <label class="block text-xs font-medium text-aux-gray mb-2">设计负责人</label>
                    <select x-model="ownerFilter" @change="filterProjects"
                            class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">全部负责人</option>
                        <option value="张工">张工</option>
                        <option value="李工">李工</option>
                        <option value="王工">王工</option>
                    </select>
                </div>

                <!-- 时间筛选 -->
                <div class="mb-4">
                    <label class="block text-xs font-medium text-aux-gray mb-2">创建时间</label>
                    <div class="space-y-2">
                        <input type="date" x-model="dateRange.start" @change="filterProjects"
                               class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <input type="date" x-model="dateRange.end" @change="filterProjects"
                               class="w-full px-3 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间项目列表区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 搜索工具栏 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text" x-model="searchQuery" @input="handleSearch"
                                   placeholder="搜索项目名称或编号..."
                                   class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary">
                            <svg class="absolute left-3 top-2.5 h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- 排序选择 -->
                    <select x-model="sortBy" @change="sortProjects"
                            class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="createTime">按创建时间</option>
                        <option value="updateTime">按更新时间</option>
                        <option value="progress">按进度</option>
                        <option value="deadline">按截止时间</option>
                    </select>

                    <!-- 批量操作 -->
                    <div class="flex space-x-2" x-show="selectedProjects.length > 0">
                        <button @click="batchReview" class="px-3 py-2 text-sm bg-warning text-white rounded-md hover:bg-yellow-600">
                            批量评审
                        </button>
                        <button @click="batchExport" class="px-3 py-2 text-sm bg-success text-white rounded-md hover:bg-green-600">
                            批量导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 项目列表内容 -->
            <div class="flex-1 overflow-auto bg-white">
                <table class="min-w-full">
                    <thead class="bg-bg-gray">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">项目信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">产品类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">设计状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">设计进度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">责任人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">计划完成</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-border-gray">
                        <template x-for="project in filteredProjects" :key="project.id">
                            <tr class="hover:bg-gray-50" :class="selectedProjects.includes(project.id) ? 'bg-blue-50' : ''">
                                <td class="px-6 py-4">
                                    <input type="checkbox" :value="project.id" x-model="selectedProjects">
                                </td>
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="text-sm font-medium text-title-gray" x-text="project.name"></div>
                                        <div class="text-xs text-aux-gray" x-text="project.code"></div>
                                        <div class="text-xs text-aux-gray mt-1" x-text="project.description"></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full" x-text="project.productType"></span>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getStatusClass(project.status)"
                                          x-text="getStatusText(project.status)"></span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-primary h-2 rounded-full" :style="`width: ${project.progress}%`"></div>
                                        </div>
                                        <span class="text-xs text-aux-gray" x-text="`${project.progress}%`"></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-2">
                                            <span class="text-xs text-primary" x-text="project.owner.charAt(0)"></span>
                                        </div>
                                        <span class="text-sm text-body-gray" x-text="project.owner"></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 text-sm text-body-gray" x-text="project.deadline"></td>
                                <td class="px-6 py-4 text-sm">
                                    <div class="flex space-x-2">
                                        <button @click="viewProject(project)" class="text-primary hover:text-blue-600">查看</button>
                                        <button @click="editProject(project)" class="text-primary hover:text-blue-600">编辑</button>
                                        <button @click="reviewProject(project)" class="text-warning hover:text-yellow-600">评审</button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white border-t border-border-gray px-6 py-3">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-aux-gray">
                        显示 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 
                        <span x-text="Math.min(currentPage * pageSize, totalItems)"></span> 条，
                        共 <span x-text="totalItems"></span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button @click="previousPage" :disabled="currentPage === 1"
                                class="px-3 py-1 text-sm border border-border-gray rounded disabled:opacity-50">
                            上一页
                        </button>
                        <button @click="nextPage" :disabled="currentPage === totalPages"
                                class="px-3 py-1 text-sm border border-border-gray rounded disabled:opacity-50">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧快速操作区域 -->
        <div class="w-80 bg-white border-l border-border-gray">
            <div class="p-6">
                <h3 class="text-lg font-medium text-title-gray mb-4">项目统计</h3>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-primary" x-text="stats.total"></div>
                        <div class="text-sm text-aux-gray">总项目数</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-success" x-text="stats.completed"></div>
                        <div class="text-sm text-aux-gray">已完成</div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-warning" x-text="stats.reviewing"></div>
                        <div class="text-sm text-aux-gray">评审中</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600" x-text="stats.designing"></div>
                        <div class="text-sm text-aux-gray">设计中</div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div>
                    <h4 class="text-sm font-medium text-title-gray mb-3">最近活动</h4>
                    <div class="space-y-3 max-h-64 overflow-y-auto">
                        <template x-for="activity in recentActivities" :key="activity.id">
                            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-md">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs"
                                     :class="getActivityColor(activity.type)">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm text-title-gray" x-text="activity.title"></div>
                                    <div class="text-xs text-aux-gray mt-1" x-text="activity.description"></div>
                                    <div class="flex items-center justify-between mt-2">
                                        <span class="text-xs text-aux-gray" x-text="activity.user"></span>
                                        <span class="text-xs text-aux-gray" x-text="activity.time"></span>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 新建项目模态框 -->
    <div x-show="showCreateModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showCreateModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">新建设计项目</h3>
                <button @click="showCreateModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="createProject" class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">项目名称 <span class="text-error">*</span></label>
                        <input type="text" x-model="createForm.name" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                               placeholder="请输入项目名称">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">产品类型 <span class="text-error">*</span></label>
                        <select x-model="createForm.productType" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择产品类型</option>
                            <option value="building">建筑玻璃</option>
                            <option value="furniture">家具玻璃</option>
                            <option value="decoration">装饰玻璃</option>
                            <option value="special">特种玻璃</option>
                        </select>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">设计负责人 <span class="text-error">*</span></label>
                        <select x-model="createForm.owner" required
                                class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择负责人</option>
                            <option value="张工">张工</option>
                            <option value="李工">李工</option>
                            <option value="王工">王工</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">计划完成时间 <span class="text-error">*</span></label>
                        <input type="date" x-model="createForm.deadline" required
                               class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">设计要求</label>
                    <textarea x-model="createForm.requirements" rows="4"
                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                              placeholder="请输入设计技术要求..."></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">项目描述</label>
                    <textarea x-model="createForm.description" rows="3"
                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                              placeholder="请输入项目描述..."></textarea>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" @click="showCreateModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" :disabled="creating"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="creating" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="creating ? '创建中...' : '确定创建'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function designApp() {
            return {
                // UI状态
                showCreateModal: false,
                creating: false,
                selectedProjects: [],
                
                // 筛选条件
                statusFilters: [],
                productTypeFilter: '',
                ownerFilter: '',
                dateRange: { start: '', end: '' },
                searchQuery: '',
                sortBy: 'createTime',
                
                // 分页
                currentPage: 1,
                pageSize: 10,
                totalItems: 0,
                
                // 表单数据
                createForm: {
                    name: '',
                    productType: '',
                    owner: '',
                    deadline: '',
                    requirements: '',
                    description: ''
                },
                
                // 数据
                projects: [
                    {
                        id: 1,
                        code: 'PRJ-2025-001',
                        name: '高强度钢化玻璃设计',
                        description: '用于高层建筑的高强度钢化玻璃产品设计',
                        productType: '建筑玻璃',
                        status: 'designing',
                        progress: 65,
                        owner: '张工',
                        deadline: '2025-08-15',
                        createTime: '2025-07-20'
                    },
                    {
                        id: 2,
                        code: 'PRJ-2025-002',
                        name: '智能调光玻璃研发',
                        description: '新型智能调光玻璃技术研发项目',
                        productType: '特种玻璃',
                        status: 'reviewing',
                        progress: 80,
                        owner: '李工',
                        deadline: '2025-09-01',
                        createTime: '2025-07-15'
                    }
                ],
                
                filteredProjects: [],
                
                stats: {
                    total: 22,
                    completed: 12,
                    reviewing: 3,
                    designing: 5
                },
                
                recentActivities: [
                    {
                        id: 1,
                        type: 'review',
                        title: '设计评审完成',
                        description: '智能调光玻璃设计评审通过',
                        user: '李工',
                        time: '2小时前'
                    },
                    {
                        id: 2,
                        type: 'create',
                        title: '新建设计项目',
                        description: '创建了高强度钢化玻璃设计项目',
                        user: '张工',
                        time: '4小时前'
                    }
                ],
                
                init() {
                    this.filteredProjects = [...this.projects];
                    this.totalItems = this.projects.length;
                },
                
                get totalPages() {
                    return Math.ceil(this.totalItems / this.pageSize);
                },
                
                get isAllSelected() {
                    return this.selectedProjects.length === this.filteredProjects.length && this.filteredProjects.length > 0;
                },
                
                filterProjects() {
                    let filtered = [...this.projects];
                    
                    // 状态筛选
                    if (this.statusFilters.length > 0) {
                        filtered = filtered.filter(project => this.statusFilters.includes(project.status));
                    }
                    
                    // 产品类型筛选
                    if (this.productTypeFilter) {
                        filtered = filtered.filter(project => project.productType === this.productTypeFilter);
                    }
                    
                    // 负责人筛选
                    if (this.ownerFilter) {
                        filtered = filtered.filter(project => project.owner === this.ownerFilter);
                    }
                    
                    // 搜索筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(project => 
                            project.name.toLowerCase().includes(query) ||
                            project.code.toLowerCase().includes(query)
                        );
                    }
                    
                    this.filteredProjects = filtered;
                    this.totalItems = filtered.length;
                    this.currentPage = 1;
                },
                
                handleSearch() {
                    this.filterProjects();
                },
                
                sortProjects() {
                    // 排序逻辑
                    this.filteredProjects.sort((a, b) => {
                        switch (this.sortBy) {
                            case 'createTime':
                                return new Date(b.createTime) - new Date(a.createTime);
                            case 'progress':
                                return b.progress - a.progress;
                            case 'deadline':
                                return new Date(a.deadline) - new Date(b.deadline);
                            default:
                                return 0;
                        }
                    });
                },
                
                toggleSelectAll() {
                    if (this.isAllSelected) {
                        this.selectedProjects = [];
                    } else {
                        this.selectedProjects = this.filteredProjects.map(p => p.id);
                    }
                },
                
                getStatusText(status) {
                    const statusMap = {
                        'designing': '设计中',
                        'reviewing': '评审中',
                        'completed': '已完成',
                        'paused': '已暂停'
                    };
                    return statusMap[status] || '未知';
                },
                
                getStatusClass(status) {
                    const classMap = {
                        'designing': 'bg-blue-100 text-blue-800',
                        'reviewing': 'bg-yellow-100 text-yellow-800',
                        'completed': 'bg-green-100 text-green-800',
                        'paused': 'bg-gray-100 text-gray-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                getActivityColor(type) {
                    const colorMap = {
                        'review': 'bg-yellow-500',
                        'create': 'bg-blue-500',
                        'complete': 'bg-green-500'
                    };
                    return colorMap[type] || 'bg-gray-500';
                },
                
                viewProject(project) {
                    alert(`查看项目: ${project.name}`);
                },
                
                editProject(project) {
                    alert(`编辑项目: ${project.name}`);
                },
                
                reviewProject(project) {
                    alert(`评审项目: ${project.name}`);
                },
                
                batchReview() {
                    alert(`批量评审 ${this.selectedProjects.length} 个项目`);
                },
                
                batchExport() {
                    alert(`批量导出 ${this.selectedProjects.length} 个项目`);
                },
                
                previousPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                },
                
                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                    }
                },
                
                createProject() {
                    this.creating = true;
                    
                    // 模拟创建过程
                    setTimeout(() => {
                        this.creating = false;
                        this.showCreateModal = false;
                        alert('设计项目创建成功！');
                        
                        // 重置表单
                        this.createForm = {
                            name: '',
                            productType: '',
                            owner: '',
                            deadline: '',
                            requirements: '',
                            description: ''
                        };
                    }, 2000);
                }
            }
        }
    </script>
</body>
</html>
