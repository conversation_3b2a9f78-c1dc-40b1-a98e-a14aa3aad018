<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术文档管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="documentApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">技术文档管理</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / 技术文档管理</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="showUploadModal = true" 
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    上传文档
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-16">
        <!-- 左侧分类导航 -->
        <div class="w-64 bg-white border-r border-border-gray">
            <div class="p-4">
                <h3 class="text-sm font-medium text-title-gray mb-3">文档分类</h3>
                <div class="space-y-1">
                    <div class="p-2 rounded cursor-pointer hover:bg-gray-50" 
                         :class="selectedCategory === 'all' ? 'bg-blue-50 text-primary' : 'text-body-gray'"
                         @click="selectedCategory = 'all'; filterDocuments()">
                        <span class="text-sm">全部文档</span>
                        <span class="text-xs text-aux-gray ml-2" x-text="`(${documents.length})`"></span>
                    </div>
                    <template x-for="category in categories" :key="category.id">
                        <div class="p-2 rounded cursor-pointer hover:bg-gray-50"
                             :class="selectedCategory === category.id ? 'bg-blue-50 text-primary' : 'text-body-gray'"
                             @click="selectedCategory = category.id; filterDocuments()">
                            <span class="text-sm" x-text="category.name"></span>
                            <span class="text-xs text-aux-gray ml-2" x-text="`(${category.count})`"></span>
                        </div>
                    </template>
                </div>
            </div>
        </div>

        <!-- 中间文档列表区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 搜索和筛选工具栏 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text" x-model="searchQuery" @input="handleSearch"
                                   placeholder="搜索文档名称或内容..."
                                   class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary">
                            <svg class="absolute left-3 top-2.5 h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- 状态筛选 -->
                    <select x-model="filterStatus" @change="filterDocuments"
                            class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">全部状态</option>
                        <option value="draft">草稿</option>
                        <option value="published">已发布</option>
                        <option value="archived">已归档</option>
                    </select>

                    <!-- 视图切换 -->
                    <div class="flex border border-border-gray rounded-md">
                        <button @click="viewMode = 'list'" 
                                :class="viewMode === 'list' ? 'bg-primary text-white' : 'bg-white text-body-gray'"
                                class="px-3 py-2 text-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'"
                                :class="viewMode === 'grid' ? 'bg-primary text-white' : 'bg-white text-body-gray'"
                                class="px-3 py-2 text-sm border-l border-border-gray">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 文档列表内容 -->
            <div class="flex-1 overflow-auto bg-white">
                <!-- 列表视图 -->
                <div x-show="viewMode === 'list'">
                    <table class="min-w-full">
                        <thead class="bg-bg-gray">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">文档名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">分类</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">大小</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">上传时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-border-gray">
                            <template x-for="doc in filteredDocuments" :key="doc.id">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 mr-3 flex items-center justify-center bg-blue-100 rounded">
                                                <svg class="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-title-gray" x-text="doc.name"></div>
                                                <div class="text-xs text-aux-gray" x-text="doc.description"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-body-gray" x-text="doc.category"></td>
                                    <td class="px-6 py-4">
                                        <span class="px-2 py-1 text-xs rounded-full"
                                              :class="getStatusClass(doc.status)"
                                              x-text="getStatusText(doc.status)"></span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-body-gray" x-text="doc.size"></td>
                                    <td class="px-6 py-4 text-sm text-body-gray" x-text="doc.uploadTime"></td>
                                    <td class="px-6 py-4 text-sm">
                                        <div class="flex space-x-2">
                                            <button @click="previewDocument(doc)" class="text-primary hover:text-blue-600">预览</button>
                                            <button @click="downloadDocument(doc)" class="text-primary hover:text-blue-600">下载</button>
                                            <button @click="editDocument(doc)" class="text-primary hover:text-blue-600">编辑</button>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- 网格视图 -->
                <div x-show="viewMode === 'grid'" class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        <template x-for="doc in filteredDocuments" :key="doc.id">
                            <div class="bg-white border border-border-gray rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                                 @click="previewDocument(doc)">
                                <div class="flex items-center justify-center w-16 h-16 mx-auto mb-3 bg-blue-100 rounded-lg">
                                    <svg class="w-8 h-8 text-primary" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"/>
                                    </svg>
                                </div>
                                <h3 class="text-sm font-medium text-title-gray text-center mb-2" x-text="doc.name"></h3>
                                <div class="flex justify-between items-center text-xs text-aux-gray">
                                    <span x-text="doc.size"></span>
                                    <span class="px-2 py-1 rounded-full"
                                          :class="getStatusClass(doc.status)"
                                          x-text="getStatusText(doc.status)"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧预览区域 -->
        <div class="w-96 bg-white border-l border-border-gray" x-show="selectedDocument">
            <div class="p-6">
                <h3 class="text-lg font-medium text-title-gray mb-4">文档详情</h3>
                <template x-if="selectedDocument">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-1">文档名称</label>
                            <p class="text-sm text-body-gray" x-text="selectedDocument.name"></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-1">文档描述</label>
                            <p class="text-sm text-body-gray" x-text="selectedDocument.description"></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-1">关联对象</label>
                            <div class="space-y-2">
                                <template x-for="relation in selectedDocument.relations" :key="relation.id">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                        <span class="text-sm text-body-gray" x-text="`${relation.type}: ${relation.name}`"></span>
                                        <button class="text-xs text-primary hover:text-blue-600">查看</button>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </main>

    <!-- 上传文档模态框 -->
    <div x-show="showUploadModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showUploadModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">上传技术文档</h3>
                <button @click="showUploadModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            
            <form @submit.prevent="uploadDocument" class="space-y-4">
                <!-- 文件上传区域 -->
                <div class="border-2 border-dashed border-border-gray rounded-lg p-6 text-center">
                    <svg class="mx-auto h-12 w-12 text-aux-gray" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <div class="mt-4">
                        <label class="cursor-pointer">
                            <span class="mt-2 block text-sm font-medium text-title-gray">点击上传文件或拖拽文件到此区域</span>
                            <input type="file" class="hidden" multiple @change="handleFileSelect">
                        </label>
                        <p class="mt-1 text-xs text-aux-gray">支持 PDF、DWG、DXF、DOC、XLS、PPT、JPG 等格式，单个文件不超过100MB</p>
                    </div>
                </div>

                <!-- 文档信息 -->
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">文档分类</label>
                        <select x-model="uploadForm.category" class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="">请选择分类</option>
                            <template x-for="category in categories" :key="category.id">
                                <option :value="category.id" x-text="category.name"></option>
                            </template>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-title-gray mb-2">访问权限</label>
                        <select x-model="uploadForm.permission" class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                            <option value="public">公开</option>
                            <option value="internal">内部</option>
                            <option value="restricted">受限</option>
                        </select>
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-title-gray mb-2">文档描述</label>
                    <textarea x-model="uploadForm.description" rows="3" 
                              class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                              placeholder="请输入文档描述..."></textarea>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" @click="showUploadModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" :disabled="uploading"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="uploading" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="uploading ? '上传中...' : '确定上传'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function documentApp() {
            return {
                // UI状态
                viewMode: 'list',
                selectedCategory: 'all',
                selectedDocument: null,
                showUploadModal: false,
                uploading: false,
                
                // 搜索和筛选
                searchQuery: '',
                filterStatus: '',
                
                // 上传表单
                uploadForm: {
                    category: '',
                    permission: 'internal',
                    description: ''
                },
                
                // 数据
                documents: [
                    {
                        id: 1,
                        name: '中空玻璃工艺规范.pdf',
                        description: '中空玻璃生产工艺标准规范文档',
                        category: '工艺规范',
                        status: 'published',
                        size: '2.5MB',
                        uploadTime: '2025-07-30 14:30',
                        relations: [
                            { id: 1, type: '物料', name: '中空玻璃-6+12A+6' },
                            { id: 2, type: 'BOM', name: 'BOM-CK-001' }
                        ]
                    },
                    {
                        id: 2,
                        name: '钢化玻璃技术图纸.dwg',
                        description: '钢化玻璃产品技术图纸',
                        category: '技术图纸',
                        status: 'published',
                        size: '5.2MB',
                        uploadTime: '2025-07-29 16:45',
                        relations: [
                            { id: 3, type: '工序', name: '钢化工序' }
                        ]
                    }
                ],
                
                categories: [
                    { id: 'process', name: '工艺规范', count: 15 },
                    { id: 'drawing', name: '技术图纸', count: 23 },
                    { id: 'manual', name: '操作手册', count: 8 },
                    { id: 'standard', name: '质量标准', count: 12 }
                ],
                
                filteredDocuments: [],
                
                init() {
                    this.filteredDocuments = [...this.documents];
                },
                
                filterDocuments() {
                    let filtered = [...this.documents];
                    
                    if (this.selectedCategory !== 'all') {
                        const category = this.categories.find(c => c.id === this.selectedCategory);
                        if (category) {
                            filtered = filtered.filter(doc => doc.category === category.name);
                        }
                    }
                    
                    if (this.filterStatus) {
                        filtered = filtered.filter(doc => doc.status === this.filterStatus);
                    }
                    
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(doc => 
                            doc.name.toLowerCase().includes(query) ||
                            doc.description.toLowerCase().includes(query)
                        );
                    }
                    
                    this.filteredDocuments = filtered;
                },
                
                handleSearch() {
                    this.filterDocuments();
                },
                
                getStatusText(status) {
                    const statusMap = {
                        'draft': '草稿',
                        'published': '已发布',
                        'archived': '已归档'
                    };
                    return statusMap[status] || '未知';
                },
                
                getStatusClass(status) {
                    const classMap = {
                        'draft': 'bg-yellow-100 text-yellow-800',
                        'published': 'bg-green-100 text-green-800',
                        'archived': 'bg-gray-100 text-gray-800'
                    };
                    return classMap[status] || 'bg-gray-100 text-gray-800';
                },
                
                previewDocument(doc) {
                    this.selectedDocument = doc;
                },
                
                downloadDocument(doc) {
                    alert(`下载文档: ${doc.name}`);
                },
                
                editDocument(doc) {
                    alert(`编辑文档: ${doc.name}`);
                },
                
                handleFileSelect(event) {
                    const files = event.target.files;
                    console.log('选择的文件:', files);
                },
                
                uploadDocument() {
                    this.uploading = true;
                    
                    // 模拟上传过程
                    setTimeout(() => {
                        this.uploading = false;
                        this.showUploadModal = false;
                        alert('文档上传成功！');
                        
                        // 重置表单
                        this.uploadForm = {
                            category: '',
                            permission: 'internal',
                            description: ''
                        };
                    }, 2000);
                }
            }
        }
    </script>
</body>
</html>
