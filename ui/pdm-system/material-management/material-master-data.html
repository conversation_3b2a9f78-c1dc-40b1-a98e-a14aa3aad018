<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>物料主数据管理 - 玻璃深加工ERP系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1890FF',
                        success: '#52C41A',
                        warning: '#FAAD14',
                        error: '#F5222D',
                        'title-gray': '#262626',
                        'body-gray': '#595959',
                        'aux-gray': '#8C8C8C',
                        'disabled-gray': '#BFBFBF',
                        'border-gray': '#D9D9D9',
                        'bg-gray': '#FAFAFA'
                    },
                    fontFamily: {
                        'chinese': ['PingFang SC', 'Microsoft YaHei', '苹方', '微软雅黑', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-bg-gray font-chinese" x-data="materialApp()">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-border-gray h-16">
        <div class="flex items-center justify-between px-6 h-full">
            <div class="flex items-center space-x-4">
                <h1 class="text-xl font-medium text-title-gray">物料主数据管理</h1>
                <span class="text-aux-gray text-sm">/ 工艺管理子系统 / 物料主数据管理</span>
            </div>
            <div class="flex items-center space-x-3">
                <button @click="showImportModal = true" 
                        class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                    </svg>
                    批量导入
                </button>
                <button @click="showCreateModal = true" 
                        class="px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors">
                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    新增物料
                </button>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="flex h-screen pt-1">
        <!-- 左侧分类区域 -->
        <div class="w-64 bg-white border-r border-border-gray">
            <div class="p-4">
                <!-- 分类切换标签 -->
                <div class="flex mb-4">
                    <button @click="categoryView = 'material'" 
                            :class="categoryView === 'material' ? 'bg-primary text-white' : 'bg-white text-body-gray border border-border-gray'"
                            class="flex-1 px-3 py-2 text-sm rounded-l-md">
                        物料分类
                    </button>
                    <button @click="categoryView = 'product'" 
                            :class="categoryView === 'product' ? 'bg-success text-white' : 'bg-white text-body-gray border border-border-gray'"
                            class="flex-1 px-3 py-2 text-sm rounded-r-md">
                        产品分类
                    </button>
                </div>

                <!-- 分类搜索 -->
                <div class="mb-4">
                    <div class="relative">
                        <input type="text" x-model="categorySearch" @input="filterCategories"
                               placeholder="搜索分类..."
                               class="w-full pl-8 pr-4 py-2 text-sm border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <svg class="absolute left-2 top-2.5 h-4 w-4 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>

                <!-- 物料分类树 -->
                <div x-show="categoryView === 'material'" class="space-y-1">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-sm font-medium text-title-gray">物料分类</h3>
                        <button @click="showCategoryModal = true" class="text-primary hover:text-blue-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                        </button>
                    </div>
                    <template x-for="category in filteredMaterialCategories" :key="category.id">
                        <div class="space-y-1">
                            <div class="flex items-center p-2 rounded cursor-pointer hover:bg-gray-50"
                                 :class="selectedCategory?.id === category.id ? 'bg-blue-50 border-l-2 border-primary' : ''"
                                 @click="selectCategory(category)">
                                <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                <span class="text-sm text-body-gray" x-text="category.name"></span>
                                <span class="text-xs text-aux-gray ml-auto" x-text="`(${category.count})`"></span>
                            </div>
                            <!-- 子分类 -->
                            <template x-if="category.children && category.children.length > 0">
                                <div class="ml-4 space-y-1">
                                    <template x-for="child in category.children" :key="child.id">
                                        <div class="flex items-center p-2 rounded cursor-pointer hover:bg-gray-50"
                                             :class="selectedCategory?.id === child.id ? 'bg-blue-50 border-l-2 border-primary' : ''"
                                             @click="selectCategory(child)">
                                            <svg class="w-3 h-3 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                            </svg>
                                            <span class="text-sm text-body-gray" x-text="child.name"></span>
                                            <span class="text-xs text-aux-gray ml-auto" x-text="`(${child.count})`"></span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>

                <!-- 产品分类树 -->
                <div x-show="categoryView === 'product'" class="space-y-1">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-sm font-medium text-title-gray">产品分类</h3>
                        <button @click="showProductCategoryModal = true" class="text-success hover:text-green-600">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                        </button>
                    </div>
                    <template x-for="category in filteredProductCategories" :key="category.id">
                        <div class="space-y-1">
                            <div class="flex items-center p-2 rounded cursor-pointer hover:bg-green-50"
                                 :class="selectedCategory?.id === category.id ? 'bg-green-50 border-l-2 border-success' : ''"
                                 @click="selectCategory(category)">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                </svg>
                                <span class="text-sm text-body-gray" x-text="category.name"></span>
                                <span class="text-xs text-aux-gray ml-auto" x-text="`(${category.count})`"></span>
                            </div>
                            <!-- 子分类 -->
                            <template x-if="category.children && category.children.length > 0">
                                <div class="ml-4 space-y-1">
                                    <template x-for="child in category.children" :key="child.id">
                                        <div class="flex items-center p-2 rounded cursor-pointer hover:bg-green-50"
                                             :class="selectedCategory?.id === child.id ? 'bg-green-50 border-l-2 border-success' : ''"
                                             @click="selectCategory(child)">
                                            <svg class="w-3 h-3 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                            </svg>
                                            <span class="text-sm text-body-gray" x-text="child.name"></span>
                                            <span class="text-xs text-aux-gray ml-auto" x-text="`(${child.count})`"></span>
                                        </div>
                                    </template>
                                </div>
                            </template>
                        </div>
                    </template>
                </div>

                <!-- 变体配置入口 -->
                <div class="mt-6 pt-4 border-t border-border-gray">
                    <button @click="showVariantConfigModal = true" 
                            class="w-full px-3 py-2 text-sm text-primary border border-primary rounded-md hover:bg-blue-50">
                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        变体配置
                    </button>
                </div>
            </div>
        </div>

        <!-- 中间物料列表区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 搜索工具栏 -->
            <div class="bg-white border-b border-border-gray px-6 py-4">
                <div class="flex items-center space-x-4">
                    <!-- 搜索框 -->
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text" x-model="searchQuery" @input="handleSearch"
                                   placeholder="搜索物料编码、名称或规格..."
                                   class="w-full pl-10 pr-4 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary">
                            <svg class="absolute left-3 top-2.5 h-5 w-5 text-aux-gray" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                    
                    <!-- 物料类型筛选 -->
                    <select x-model="typeFilter" @change="filterMaterials"
                            class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">全部类型</option>
                        <option value="原材料">原材料</option>
                        <option value="半成品">半成品</option>
                        <option value="成品">成品</option>
                        <option value="辅料">辅料</option>
                    </select>

                    <!-- 状态筛选 -->
                    <select x-model="statusFilter" @change="filterMaterials"
                            class="px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="">全部状态</option>
                        <option value="启用">启用</option>
                        <option value="禁用">禁用</option>
                    </select>

                    <!-- 批量操作 -->
                    <div class="flex space-x-2" x-show="selectedMaterials.length > 0">
                        <button @click="batchEnable" class="px-3 py-2 text-sm bg-success text-white rounded-md hover:bg-green-600">
                            批量启用
                        </button>
                        <button @click="batchDisable" class="px-3 py-2 text-sm bg-warning text-white rounded-md hover:bg-yellow-600">
                            批量禁用
                        </button>
                        <button @click="batchExport" class="px-3 py-2 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                            批量导出
                        </button>
                    </div>
                </div>
            </div>

            <!-- 物料列表内容 -->
            <div class="flex-1 overflow-auto bg-white">
                <table class="min-w-full">
                    <thead class="bg-bg-gray">
                        <tr>
                            <th class="px-6 py-3 text-left">
                                <input type="checkbox" @change="toggleSelectAll" :checked="isAllSelected">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">物料信息</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">规格</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">变体</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-aux-gray uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-border-gray">
                        <template x-for="material in filteredMaterials" :key="material.id">
                            <tr class="hover:bg-gray-50" :class="selectedMaterials.includes(material.id) ? 'bg-blue-50' : ''">
                                <td class="px-6 py-4">
                                    <input type="checkbox" :value="material.id" x-model="selectedMaterials">
                                </td>
                                <td class="px-6 py-4">
                                    <div>
                                        <div class="text-sm font-medium text-title-gray" x-text="material.name"></div>
                                        <div class="text-xs text-aux-gray" x-text="material.code"></div>
                                        <div class="text-xs text-aux-gray" x-text="material.category"></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getTypeClass(material.type)"
                                          x-text="material.type"></span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-body-gray" x-text="material.specification"></div>
                                    <template x-if="material.glassAttributes">
                                        <div class="text-xs text-aux-gray mt-1">
                                            <span x-text="`${material.glassAttributes.length}×${material.glassAttributes.width}×${material.glassAttributes.thickness}mm`"></span>
                                            <span x-text="material.glassAttributes.color" class="ml-2"></span>
                                        </div>
                                    </template>
                                </td>
                                <td class="px-6 py-4 text-sm text-body-gray" x-text="material.unit"></td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="material.status === '启用' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                                          x-text="material.status"></span>
                                </td>
                                <td class="px-6 py-4">
                                    <template x-if="material.hasVariants">
                                        <button @click="viewVariants(material)" class="text-primary hover:text-blue-600 text-sm">
                                            <span x-text="`${material.variantCount}个变体`"></span>
                                        </button>
                                    </template>
                                    <template x-if="!material.hasVariants">
                                        <span class="text-aux-gray text-sm">-</span>
                                    </template>
                                </td>
                                <td class="px-6 py-4 text-sm">
                                    <div class="flex space-x-2">
                                        <button @click="viewMaterial(material)" class="text-primary hover:text-blue-600">查看</button>
                                        <button @click="editMaterial(material)" class="text-primary hover:text-blue-600">编辑</button>
                                        <button @click="toggleMaterialStatus(material)" 
                                                :class="material.status === '启用' ? 'text-warning hover:text-yellow-600' : 'text-success hover:text-green-600'"
                                                x-text="material.status === '启用' ? '禁用' : '启用'"></button>
                                    </div>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white border-t border-border-gray px-6 py-3">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-aux-gray">
                        显示 <span x-text="(currentPage - 1) * pageSize + 1"></span> 到 
                        <span x-text="Math.min(currentPage * pageSize, totalItems)"></span> 条，
                        共 <span x-text="totalItems"></span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button @click="previousPage" :disabled="currentPage === 1"
                                class="px-3 py-1 text-sm border border-border-gray rounded disabled:opacity-50">
                            上一页
                        </button>
                        <button @click="nextPage" :disabled="currentPage === totalPages"
                                class="px-3 py-1 text-sm border border-border-gray rounded disabled:opacity-50">
                            下一页
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧详情面板 -->
        <div class="w-96 bg-white border-l border-border-gray" x-show="selectedMaterial">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-title-gray">物料详情</h3>
                    <button @click="selectedMaterial = null" class="text-aux-gray hover:text-title-gray">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <template x-if="selectedMaterial">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">基本信息</h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">物料编码</label>
                                    <div class="text-sm text-body-gray" x-text="selectedMaterial.code"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">物料名称</label>
                                    <div class="text-sm text-body-gray" x-text="selectedMaterial.name"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">物料类型</label>
                                    <span class="px-2 py-1 text-xs rounded-full"
                                          :class="getTypeClass(selectedMaterial.type)"
                                          x-text="selectedMaterial.type"></span>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">规格型号</label>
                                    <div class="text-sm text-body-gray" x-text="selectedMaterial.specification"></div>
                                </div>
                                <div>
                                    <label class="block text-xs text-aux-gray mb-1">计量单位</label>
                                    <div class="text-sm text-body-gray" x-text="selectedMaterial.unit"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 玻璃特殊属性 -->
                        <template x-if="selectedMaterial.glassAttributes">
                            <div>
                                <h4 class="text-sm font-medium text-title-gray mb-3">玻璃属性</h4>
                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-xs text-aux-gray mb-1">长度(mm)</label>
                                        <div class="text-sm text-body-gray" x-text="selectedMaterial.glassAttributes.length"></div>
                                    </div>
                                    <div>
                                        <label class="block text-xs text-aux-gray mb-1">宽度(mm)</label>
                                        <div class="text-sm text-body-gray" x-text="selectedMaterial.glassAttributes.width"></div>
                                    </div>
                                    <div>
                                        <label class="block text-xs text-aux-gray mb-1">厚度(mm)</label>
                                        <div class="text-sm text-body-gray" x-text="selectedMaterial.glassAttributes.thickness"></div>
                                    </div>
                                    <div>
                                        <label class="block text-xs text-aux-gray mb-1">颜色</label>
                                        <div class="text-sm text-body-gray" x-text="selectedMaterial.glassAttributes.color"></div>
                                    </div>
                                    <div class="col-span-2">
                                        <label class="block text-xs text-aux-gray mb-1">品级</label>
                                        <div class="text-sm text-body-gray" x-text="selectedMaterial.glassAttributes.grade"></div>
                                    </div>
                                </div>
                            </div>
                        </template>

                        <!-- 变体信息 -->
                        <template x-if="selectedMaterial.hasVariants">
                            <div>
                                <h4 class="text-sm font-medium text-title-gray mb-3">变体管理</h4>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-body-gray">变体数量</span>
                                        <span class="text-sm font-medium text-title-gray" x-text="selectedMaterial.variantCount"></span>
                                    </div>
                                    <button @click="viewVariants(selectedMaterial)" 
                                            class="w-full px-3 py-2 text-sm bg-primary text-white rounded-md hover:bg-blue-600">
                                        管理变体
                                    </button>
                                </div>
                            </div>
                        </template>

                        <!-- 使用统计 -->
                        <div>
                            <h4 class="text-sm font-medium text-title-gray mb-3">使用统计</h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">BOM使用次数</span>
                                    <span class="text-sm font-medium text-title-gray" x-text="selectedMaterial.bomUsageCount || 0"></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-body-gray">最近使用时间</span>
                                    <span class="text-sm text-body-gray" x-text="selectedMaterial.lastUsedTime || '未使用'"></span>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="space-y-2">
                            <button @click="editMaterial(selectedMaterial)" 
                                    class="w-full px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600">
                                编辑物料
                            </button>
                            <button @click="duplicateMaterial(selectedMaterial)" 
                                    class="w-full px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                                复制物料
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </main>

    <!-- 新增物料模态框 -->
    <div x-show="showCreateModal"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showCreateModal = false">
        <div class="relative top-20 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white" @click.stop>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-title-gray">新增物料</h3>
                <button @click="showCreateModal = false" class="text-aux-gray hover:text-title-gray">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <form @submit.prevent="createMaterial" class="space-y-6">
                <!-- 基本信息 -->
                <div>
                    <h4 class="text-sm font-medium text-title-gray mb-4">基本信息</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">物料编码 <span class="text-error">*</span></label>
                            <div class="flex">
                                <input type="text" x-model="createForm.code" required
                                       class="flex-1 px-3 py-2 border border-border-gray rounded-l-md focus:outline-none focus:ring-1 focus:ring-primary"
                                       placeholder="系统自动生成或手动输入">
                                <button type="button" @click="generateCode"
                                        class="px-3 py-2 bg-gray-100 border border-l-0 border-border-gray rounded-r-md hover:bg-gray-200">
                                    生成
                                </button>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">物料名称 <span class="text-error">*</span></label>
                            <input type="text" x-model="createForm.name" required
                                   class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                   placeholder="请输入物料名称">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">物料类型 <span class="text-error">*</span></label>
                            <select x-model="createForm.type" required @change="onTypeChange"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择物料类型</option>
                                <option value="原材料">原材料</option>
                                <option value="半成品">半成品</option>
                                <option value="成品">成品</option>
                                <option value="辅料">辅料</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">计量单位 <span class="text-error">*</span></label>
                            <select x-model="createForm.unit" required
                                    class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择单位</option>
                                <option value="片">片</option>
                                <option value="米">米</option>
                                <option value="公斤">公斤</option>
                                <option value="套">套</option>
                                <option value="个">个</option>
                            </select>
                        </div>
                        <div class="col-span-2">
                            <label class="block text-sm font-medium text-title-gray mb-2">规格型号</label>
                            <input type="text" x-model="createForm.specification"
                                   class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                   placeholder="请输入规格型号">
                        </div>
                    </div>
                </div>

                <!-- 分类信息 -->
                <div>
                    <h4 class="text-sm font-medium text-title-gray mb-4">分类信息</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">物料分类 <span class="text-error">*</span></label>
                            <select x-model="createForm.categoryId" required
                                    class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择物料分类</option>
                                <template x-for="category in materialCategories" :key="category.id">
                                    <optgroup :label="category.name">
                                        <template x-for="child in category.children" :key="child.id">
                                            <option :value="child.id" x-text="child.name"></option>
                                        </template>
                                    </optgroup>
                                </template>
                            </select>
                        </div>
                        <div x-show="createForm.type === '成品' || createForm.type === '半成品'">
                            <label class="block text-sm font-medium text-title-gray mb-2">产品分类</label>
                            <select x-model="createForm.productCategoryId"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择产品分类</option>
                                <template x-for="category in productCategories" :key="category.id">
                                    <optgroup :label="category.name">
                                        <template x-for="child in category.children" :key="child.id">
                                            <option :value="child.id" x-text="child.name"></option>
                                        </template>
                                    </optgroup>
                                </template>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 玻璃特殊属性 -->
                <div x-show="showGlassAttributes">
                    <h4 class="text-sm font-medium text-title-gray mb-4">玻璃属性</h4>
                    <div class="grid grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">长度(mm)</label>
                            <input type="number" x-model="createForm.glassAttributes.length" min="0"
                                   class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                   placeholder="长度">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">宽度(mm)</label>
                            <input type="number" x-model="createForm.glassAttributes.width" min="0"
                                   class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                   placeholder="宽度">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">厚度(mm)</label>
                            <input type="number" x-model="createForm.glassAttributes.thickness" min="0" step="0.1"
                                   class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary"
                                   placeholder="厚度">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">颜色</label>
                            <select x-model="createForm.glassAttributes.color"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择颜色</option>
                                <option value="透明">透明</option>
                                <option value="超白">超白</option>
                                <option value="茶色">茶色</option>
                                <option value="灰色">灰色</option>
                                <option value="蓝色">蓝色</option>
                                <option value="绿色">绿色</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-title-gray mb-2">品级</label>
                            <select x-model="createForm.glassAttributes.grade"
                                    class="w-full px-3 py-2 border border-border-gray rounded-md focus:outline-none focus:ring-1 focus:ring-primary">
                                <option value="">请选择品级</option>
                                <option value="A级">A级</option>
                                <option value="B级">B级</option>
                                <option value="C级">C级</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 变体管理 -->
                <div>
                    <h4 class="text-sm font-medium text-title-gray mb-4">变体管理</h4>
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" x-model="createForm.enableVariants" class="mr-2">
                            <span class="text-sm text-title-gray">启用变体管理</span>
                        </label>
                        <div x-show="createForm.enableVariants" class="text-sm text-aux-gray">
                            此物料将作为变体主物料，可创建多个规格变体
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 pt-4 border-t border-border-gray">
                    <button type="button" @click="showCreateModal = false"
                            class="px-4 py-2 text-sm font-medium text-body-gray bg-white border border-border-gray rounded-md hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" :disabled="creating"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-blue-600 disabled:bg-disabled-gray">
                        <span x-show="creating" class="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                        <span x-text="creating ? '创建中...' : '确定创建'"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function materialApp() {
            return {
                // UI状态
                categoryView: 'material', // 'material' 或 'product'
                selectedCategory: null,
                selectedMaterial: null,
                selectedMaterials: [],
                showCreateModal: false,
                showImportModal: false,
                showCategoryModal: false,
                showProductCategoryModal: false,
                showVariantConfigModal: false,
                creating: false,

                // 表单数据
                createForm: {
                    code: '',
                    name: '',
                    type: '',
                    specification: '',
                    unit: '',
                    categoryId: '',
                    productCategoryId: '',
                    enableVariants: false,
                    glassAttributes: {
                        length: '',
                        width: '',
                        thickness: '',
                        color: '',
                        grade: ''
                    }
                },
                
                // 搜索和筛选
                categorySearch: '',
                searchQuery: '',
                typeFilter: '',
                statusFilter: '',
                
                // 分页
                currentPage: 1,
                pageSize: 20,
                totalItems: 0,
                
                // 数据
                materialCategories: [
                    {
                        id: 1,
                        name: '原材料',
                        count: 45,
                        children: [
                            { id: 11, name: '玻璃原片', count: 25 },
                            { id: 12, name: '胶水辅料', count: 12 },
                            { id: 13, name: '五金配件', count: 8 }
                        ]
                    },
                    {
                        id: 2,
                        name: '半成品',
                        count: 32,
                        children: [
                            { id: 21, name: '切割件', count: 18 },
                            { id: 22, name: '磨边件', count: 14 }
                        ]
                    },
                    {
                        id: 3,
                        name: '成品',
                        count: 28,
                        children: [
                            { id: 31, name: '钢化玻璃', count: 15 },
                            { id: 32, name: '夹胶玻璃', count: 13 }
                        ]
                    }
                ],
                
                productCategories: [
                    {
                        id: 101,
                        name: '建筑玻璃',
                        count: 35,
                        children: [
                            { id: 111, name: '幕墙玻璃', count: 20 },
                            { id: 112, name: '门窗玻璃', count: 15 }
                        ]
                    },
                    {
                        id: 102,
                        name: '家具玻璃',
                        count: 25,
                        children: [
                            { id: 121, name: '桌面玻璃', count: 15 },
                            { id: 122, name: '柜门玻璃', count: 10 }
                        ]
                    }
                ],
                
                materials: [
                    {
                        id: 1,
                        code: 'RM-001',
                        name: '超白玻璃原片',
                        type: '原材料',
                        specification: '2440×3660×6mm',
                        unit: '片',
                        status: '启用',
                        category: '玻璃原片',
                        hasVariants: true,
                        variantCount: 8,
                        bomUsageCount: 15,
                        lastUsedTime: '2025-07-30',
                        glassAttributes: {
                            length: 2440,
                            width: 3660,
                            thickness: 6,
                            color: '超白',
                            grade: 'A级'
                        }
                    },
                    {
                        id: 2,
                        code: 'SP-001',
                        name: '钢化玻璃半成品',
                        type: '半成品',
                        specification: '1200×800×8mm',
                        unit: '片',
                        status: '启用',
                        category: '切割件',
                        hasVariants: false,
                        variantCount: 0,
                        bomUsageCount: 8,
                        lastUsedTime: '2025-07-29',
                        glassAttributes: {
                            length: 1200,
                            width: 800,
                            thickness: 8,
                            color: '透明',
                            grade: 'A级'
                        }
                    }
                ],
                
                filteredMaterialCategories: [],
                filteredProductCategories: [],
                filteredMaterials: [],
                
                init() {
                    this.filteredMaterialCategories = [...this.materialCategories];
                    this.filteredProductCategories = [...this.productCategories];
                    this.filteredMaterials = [...this.materials];
                    this.totalItems = this.materials.length;
                },
                
                get totalPages() {
                    return Math.ceil(this.totalItems / this.pageSize);
                },
                
                get isAllSelected() {
                    return this.selectedMaterials.length === this.filteredMaterials.length && this.filteredMaterials.length > 0;
                },

                get showGlassAttributes() {
                    return this.createForm.type && ['原材料', '半成品', '成品'].includes(this.createForm.type);
                },
                
                filterCategories() {
                    const query = this.categorySearch.toLowerCase();
                    if (this.categoryView === 'material') {
                        this.filteredMaterialCategories = this.materialCategories.filter(cat => 
                            cat.name.toLowerCase().includes(query) ||
                            (cat.children && cat.children.some(child => child.name.toLowerCase().includes(query)))
                        );
                    } else {
                        this.filteredProductCategories = this.productCategories.filter(cat => 
                            cat.name.toLowerCase().includes(query) ||
                            (cat.children && cat.children.some(child => child.name.toLowerCase().includes(query)))
                        );
                    }
                },
                
                selectCategory(category) {
                    this.selectedCategory = category;
                    this.filterMaterials();
                },
                
                filterMaterials() {
                    let filtered = [...this.materials];
                    
                    // 分类筛选
                    if (this.selectedCategory) {
                        filtered = filtered.filter(material => 
                            material.category === this.selectedCategory.name
                        );
                    }
                    
                    // 类型筛选
                    if (this.typeFilter) {
                        filtered = filtered.filter(material => material.type === this.typeFilter);
                    }
                    
                    // 状态筛选
                    if (this.statusFilter) {
                        filtered = filtered.filter(material => material.status === this.statusFilter);
                    }
                    
                    // 搜索筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(material => 
                            material.name.toLowerCase().includes(query) ||
                            material.code.toLowerCase().includes(query) ||
                            material.specification.toLowerCase().includes(query)
                        );
                    }
                    
                    this.filteredMaterials = filtered;
                    this.totalItems = filtered.length;
                    this.currentPage = 1;
                },
                
                handleSearch() {
                    this.filterMaterials();
                },
                
                toggleSelectAll() {
                    if (this.isAllSelected) {
                        this.selectedMaterials = [];
                    } else {
                        this.selectedMaterials = this.filteredMaterials.map(m => m.id);
                    }
                },
                
                getTypeClass(type) {
                    const classMap = {
                        '原材料': 'bg-blue-100 text-blue-800',
                        '半成品': 'bg-yellow-100 text-yellow-800',
                        '成品': 'bg-green-100 text-green-800',
                        '辅料': 'bg-purple-100 text-purple-800'
                    };
                    return classMap[type] || 'bg-gray-100 text-gray-800';
                },
                
                viewMaterial(material) {
                    this.selectedMaterial = material;
                },
                
                editMaterial(material) {
                    alert(`编辑物料: ${material.name}`);
                },
                
                toggleMaterialStatus(material) {
                    material.status = material.status === '启用' ? '禁用' : '启用';
                    alert(`物料 ${material.name} 已${material.status}`);
                },
                
                viewVariants(material) {
                    alert(`查看物料 ${material.name} 的变体管理`);
                },
                
                duplicateMaterial(material) {
                    alert(`复制物料: ${material.name}`);
                },
                
                batchEnable() {
                    alert(`批量启用 ${this.selectedMaterials.length} 个物料`);
                },
                
                batchDisable() {
                    alert(`批量禁用 ${this.selectedMaterials.length} 个物料`);
                },
                
                batchExport() {
                    alert(`批量导出 ${this.selectedMaterials.length} 个物料`);
                },
                
                previousPage() {
                    if (this.currentPage > 1) {
                        this.currentPage--;
                    }
                },
                
                nextPage() {
                    if (this.currentPage < this.totalPages) {
                        this.currentPage++;
                    }
                },

                // 表单相关方法
                generateCode() {
                    const typePrefix = {
                        '原材料': 'RM',
                        '半成品': 'SP',
                        '成品': 'FP',
                        '辅料': 'AU'
                    };
                    const prefix = typePrefix[this.createForm.type] || 'MT';
                    const timestamp = Date.now().toString().slice(-6);
                    this.createForm.code = `${prefix}-${timestamp}`;
                },

                onTypeChange() {
                    // 类型变化时重置相关字段
                    this.createForm.productCategoryId = '';
                    if (!this.showGlassAttributes) {
                        this.createForm.glassAttributes = {
                            length: '',
                            width: '',
                            thickness: '',
                            color: '',
                            grade: ''
                        };
                    }
                },

                resetCreateForm() {
                    this.createForm = {
                        code: '',
                        name: '',
                        type: '',
                        specification: '',
                        unit: '',
                        categoryId: '',
                        productCategoryId: '',
                        enableVariants: false,
                        glassAttributes: {
                            length: '',
                            width: '',
                            thickness: '',
                            color: '',
                            grade: ''
                        }
                    };
                },

                async createMaterial() {
                    this.creating = true;
                    try {
                        // 模拟API调用
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // 创建新物料对象
                        const newMaterial = {
                            id: Date.now(),
                            code: this.createForm.code,
                            name: this.createForm.name,
                            type: this.createForm.type,
                            specification: this.createForm.specification,
                            unit: this.createForm.unit,
                            status: '启用',
                            category: this.getCategoryName(this.createForm.categoryId),
                            hasVariants: this.createForm.enableVariants,
                            variantCount: 0,
                            bomUsageCount: 0,
                            lastUsedTime: null,
                            glassAttributes: this.showGlassAttributes ? this.createForm.glassAttributes : null
                        };

                        // 添加到列表
                        this.materials.unshift(newMaterial);
                        this.filterMaterials();

                        // 关闭模态框并重置表单
                        this.showCreateModal = false;
                        this.resetCreateForm();

                        alert('物料创建成功！');
                    } catch (error) {
                        alert('创建失败，请重试');
                    } finally {
                        this.creating = false;
                    }
                },

                getCategoryName(categoryId) {
                    for (const category of this.materialCategories) {
                        if (category.children) {
                            for (const child of category.children) {
                                if (child.id == categoryId) {
                                    return child.name;
                                }
                            }
                        }
                    }
                    return '';
                }
            }
        }
    </script>
</body>
</html>
