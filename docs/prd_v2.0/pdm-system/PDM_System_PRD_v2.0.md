# PRD-02: 工艺管理子系统（PDM）产品需求文档

> **版本**: 2.0  
> **状态**: 重构版  
> **撰写人**: 产品团队  
> **日期**: 2025-07-30  
> **术语表**: 参考 [全局术语表](../_Glossary.md)  
> **业务规则**: 参考 [核心业务规则库](../_Business_Rules.md)

---

## 1. 核心问题与价值主张

### 1.1 核心问题
**玻璃深加工企业产品高度定制化，传统人工管理BOM和工艺路线效率低下、错误率高，无法支撑规模化生产和知识沉淀。**

### 1.2 价值主张
建立企业级产品数据管理中心，通过参数化BOM和标准化工艺路线，实现从设计到生产的数据一体化，支撑快速报价、精准生产和知识复用。

### 1.3 商业价值量化
- **报价效率提升**: 参数化BOM使复杂产品报价时间从2小时缩短至10分钟
- **生产错误率降低**: 标准化工艺路线减少生产错误80%
- **知识复用率提升**: 设计知识数字化沉淀，新产品开发效率提升60%
- **成本计算精度**: 参数化计算使物料成本精度提升至95%以上

---

## 2. 目标用户与使用场景

### 2.1 目标用户
参考 [全局术语表](../_Glossary.md) 中的用户角色定义：

| 用户角色 | 职责描述 | 核心需求 |
|----------|----------|----------|
| **工艺工程师** | 负责产品设计、BOM创建、工艺路线制定 | 需要强大的参数化设计工具和版本管理 |
| **销售代表** | 使用参数化BOM进行产品配置和报价 | 需要简单易用的产品配置界面 |
| **生产计划员** | 基于固化BOM制定生产计划 | 需要准确完整的生产BOM和工艺路线 |

### 2.2 核心使用场景

#### 场景一：参数化BOM设计
**用户故事**: 作为一个工艺工程师，我想要创建参数化的中空玻璃BOM，以便销售人员能根据客户尺寸快速生成准确报价。

**操作流程**:
1. 创建中空玻璃成品物料主数据
2. 新建参数化BOM，定义长度(L)和宽度(W)变量
3. 添加BOM行：玻璃原片、铝条、密封胶等
4. 设置用量公式：玻璃面积=L×W，铝条长度=2×(L+W)
5. 审核并激活BOM模板

**成功标准**: BOM创建完成后，销售系统能基于参数自动计算物料用量

#### 场景二：销售BOM固化为生产BOM
**用户故事**: 作为一个工艺工程师，我想要审核销售订单生成的BOM配置，以便确保生产数据的准确性。

**操作流程**:
1. 接收销售订单传递的BOM快照
2. 审核物料配置和用量计算
3. 根据生产实际情况调整：增加损耗、替换物料、添加辅料
4. 确认并固化为生产BOM
5. 生产BOM自动传递给MRP和生产系统

**成功标准**: 固化后的生产BOM数据准确，支撑后续生产执行

---

## 3. 功能需求（用户故事格式）

### 3.1 物料主数据管理

#### 需求 3.1.1: 统一物料库管理
**用户故事**: 作为一个工艺工程师，我想要维护统一的物料主数据库，以便为BOM设计提供标准化的物料信息。

**功能描述**:
- 支持原材料、半成品、成品、辅料等全类型物料管理
- 物料属性包含：编码、名称、规格、类型、单位、供应商等
- 玻璃类物料特殊属性：长、宽、厚度、颜色、品级
- 物料编码自动生成规则

**验收标准**:
- [ ] 支持物料的创建、编辑、查询、禁用操作
- [ ] 物料编码全局唯一性校验
- [ ] 玻璃类物料支持尺寸、颜色等特殊属性
- [ ] 已有业务数据的物料关键信息锁定保护
- [ ] 物料信息变更记录完整的审计日志

#### 需求 3.1.2: 物料分类管理
**用户故事**: 作为一个工艺工程师，我想要对物料进行分类管理，以便快速查找和选择所需物料。

**功能描述**:
- 支持多级物料分类体系
- 分类与物料类型关联
- 支持物料分类的批量操作
- 分类权限控制

**验收标准**:
- [ ] 支持树状物料分类结构
- [ ] 物料可按分类快速筛选
- [ ] 分类变更不影响已有业务数据
- [ ] 支持分类级别的权限控制

### 3.2 产品结构（BOM）管理

#### 需求 3.2.1: 参数化BOM设计
**用户故事**: 作为一个工艺工程师，我想要创建参数化BOM模板，以便支持复杂产品的快速配置和报价。

**功能描述**:
- 支持在BOM中定义参数变量（如长度L、宽度W、厚度T）
- BOM行物料用量支持公式计算
- 参数化规则可视化设计
- 公式语法校验和测试

**验收标准**:
- [ ] 支持定义多个参数变量
- [ ] 物料用量公式支持四则运算和常用函数
- [ ] 公式语法错误实时提示
- [ ] 参数化BOM可预览和测试
- [ ] 公式计算结果精度满足业务要求

#### 需求 3.2.2: BOM版本管理
**用户故事**: 作为一个工艺工程师，我想要管理BOM的多个版本，以便追踪产品设计变更历史。

**功能描述**:
- BOM版本控制机制
- 版本比较和差异分析
- 版本状态管理：草稿、激活、归档
- 版本变更审批流程

**验收标准**:
- [ ] 每次重大修改可生成新版本
- [ ] 只有一个版本处于激活状态
- [ ] 被生产订单引用的版本自动锁定
- [ ] 支持版本间差异对比
- [ ] 版本变更记录完整的审计轨迹

#### 需求 3.2.3: BOM固化流程
**用户故事**: 作为一个工艺工程师，我想要审核销售订单的BOM配置，以便生成准确的生产BOM。

**功能描述**:
- 销售BOM自动生成待审核快照
- 支持BOM微调：替换物料、调整用量、增加辅料
- BOM固化和锁定机制
- 固化BOM独立于原始模板

**验收标准**:
- [ ] 销售订单确认后自动生成BOM快照
- [ ] 支持对BOM快照进行微调
- [ ] 审核通过后BOM状态变为固化
- [ ] 固化BOM不受原始模板变更影响
- [ ] 重大问题可驳回至销售环节

### 3.3 工艺路线管理

#### 需求 3.3.1: 标准工艺路线设计
**用户故事**: 作为一个工艺工程师，我想要定义标准化的工艺路线，以便规范产品的生产流程。

**功能描述**:
- 工艺路线由有序工序组成
- 工序信息：工序号、名称、工作中心、工时、外协标识
- 工艺路线与物料关联
- 支持并行工序和分支流程

**验收标准**:
- [ ] 支持创建、编辑、查询工艺路线
- [ ] 工序信息完整：工序号、名称、工作中心、工时等
- [ ] 支持工序的并行和分支设计
- [ ] 工艺路线可与成品/半成品物料关联
- [ ] 被生产订单引用后自动锁定

#### 需求 3.3.2: 外协工序管理
**用户故事**: 作为一个工艺工程师，我想要标识可外协的工序，以便支持灵活的生产模式。

**功能描述**:
- 工序外协标识和管理
- 外协供应商信息关联
- 外协工序成本计算
- 内制/外协切换机制

**验收标准**:
- [ ] 工序可标识为外协或内制
- [ ] 外协工序可关联供应商信息
- [ ] 支持外协成本的单独计算
- [ ] 生产计划可根据外协标识调整

### 3.4 技术文档管理

#### 需求 3.4.1: 文档关联管理
**用户故事**: 作为一个工艺工程师，我想要将技术文档关联到物料和BOM，以便生产人员能方便查阅。

**功能描述**:
- 支持多种文档格式：CAD图纸、PDF、Word、Excel等
- 文档与物料、BOM、工序的关联
- 文档版本控制
- 文档预览和下载

**验收标准**:
- [ ] 支持上传、下载、预览技术文档
- [ ] 文档可与物料、BOM、工序关联
- [ ] 文档版本控制和历史追踪
- [ ] 文件大小和类型限制可配置
- [ ] 文档访问权限控制

### 3.5 产品设计管理

#### 需求 3.5.1: 产品设计项目管理
**用户故事**: 作为一个产品设计师，我想要规范管理产品设计项目，以便确保设计质量和进度控制。

**功能描述**:
- 产品设计项目创建和管理
- 设计团队分配和协作
- 设计进度跟踪和里程碑管理
- 设计文档版本控制

**验收标准**:
- [ ] 设计项目管理功能完整
- [ ] 设计团队协作有效
- [ ] 设计进度跟踪准确
- [ ] 设计文档管理规范

#### 需求 3.5.2: 设计评审管理
**用户故事**: 作为一个设计主管，我想要组织和管理设计评审，以便确保设计质量和技术可行性。

**功能描述**:
- 设计评审流程定义和执行
- 评审团队组织和管理
- 评审意见记录和跟踪
- 设计改进建议管理

**验收标准**:
- [ ] 设计评审流程清晰
- [ ] 评审意见记录完整
- [ ] 改进建议可追溯
- [ ] 评审效果可评估

---

## 4. 验收标准（可测试列表）

### 4.1 功能验收标准
- [ ] 参数化BOM创建后能正确计算物料用量
- [ ] BOM版本变更后历史版本可追溯
- [ ] 工艺路线关联后生产工单正确生成工序
- [ ] 技术文档关联后生产人员能正常查阅
- [ ] BOM固化后数据不受原始模板影响

### 4.2 性能验收标准
- [ ] 参数化BOM计算响应时间 < 1s（10个变量内）
- [ ] 物料查询响应时间 < 500ms（万级数据）
- [ ] BOM展开响应时间 < 2s（5级BOM结构）
- [ ] 文档上传处理时间 < 30s（100MB文件）

### 4.3 数据准确性验收标准
- [ ] 参数化计算结果精度 ≥ 99.9%
- [ ] BOM数据传递无丢失
- [ ] 工艺路线数据完整性100%
- [ ] 版本控制数据一致性100%

---

## 5. 交互设计要求

### 5.1 界面设计原则
- **专业性**: 界面设计符合工程师使用习惯
- **效率性**: 常用操作路径最短，支持快捷键
- **可视化**: 复杂的BOM结构和工艺路线图形化展示
- **容错性**: 关键操作提供预览和确认机制

### 5.2 关键界面要求
- **BOM设计器**: 树状结构 + 参数配置面板
- **工艺路线设计**: 流程图形式的可视化设计
- **物料选择器**: 分类导航 + 搜索过滤
- **版本对比**: 并排对比显示差异

---

## 6. 数据埋点需求

### 6.1 设计行为埋点
- BOM创建和修改行为
- 参数化规则设计行为
- 工艺路线设计行为
- 文档关联操作行为

### 6.2 使用效果埋点
- 参数化BOM使用频率
- BOM计算准确率
- 工艺路线执行成功率
- 文档查阅次数

### 6.3 性能监控埋点
- BOM计算耗时
- 文档加载时间
- 数据查询响应时间
- 系统并发处理能力

---

## 7. 未来展望/V-Next

### 7.1 暂不开发功能
- **AI辅助设计**: 基于历史数据的智能BOM推荐
- **3D可视化**: BOM结构的三维展示
- **移动端设计**: 移动设备上的BOM查看和审批
- **高级仿真**: 工艺路线的仿真优化

### 7.2 技术演进方向
- **云端协同**: 多地研发团队的协同设计
- **知识图谱**: 产品知识的图谱化管理
- **数字孪生**: 产品数字化模型构建

---

## 版本控制

| 版本 | 日期 | 变更说明 | 责任人 |
|------|------|----------|--------|
| 1.0 | 2025-07-29 | 初始版本 | Roo |
| 2.0 | 2025-07-30 | 重构版本，应用五大核心原则 | 产品团队 |

---

**文档状态**: 已重构 ✅  
**应用原则**: 第一性原理 ✅ DRY ✅ KISS ✅ SOLID ✅ YAGNI ✅
