# 功能模块规格说明书：工序管理模块

- **模块ID**: PDM-006
- **所属子系统**: 工艺管理子系统(PDM)
- **最后更新**: 2025-07-31

## 1. 用户故事 (User Stories)

- **As a** 工艺工程师, **I want to** 建立标准化的工序主数据库, **so that** 为工艺路线设计提供标准化的工序信息。
- **As a** 工艺工程师, **I want to** 定义工序的标准工时和质量要求, **so that** 支持精确的生产计划和成本核算。
- **As a** 工艺工程师, **I want to** 管理工序的作业指导和安全规范, **so that** 确保生产操作的标准化和安全性。
- **As a** 生产计划员, **I want to** 获取工序的标准工时信息, **so that** 制定准确的生产计划和排程。
- **As a** 质量工程师, **I want to** 查看工序的质量标准, **so that** 制定相应的质量检验方案。

## 2. 业务规则与流程 (Business Logic & Flow)

### 前置条件
- 用户具有工序管理权限
- 工艺分类体系已建立
- 质量标准体系已配置
- 设备主数据已建立

### 核心流程

#### 2.1 工序创建流程
1. 工艺工程师选择工序分类
2. 填写工序基本信息（编码、名称、描述）
3. 设置工序类型和工艺特性
4. 配置标准工时和工艺参数
5. 定义质量要求和检验标准
6. 编写作业指导和安全规范
7. 设置设备和技能要求
8. 提交工序审核
9. 审核通过后工序生效

#### 2.2 工序标准维护流程
1. 查询需要更新的工序
2. 修改工序标准信息
3. 记录变更原因和影响分析
4. 提交变更审批
5. 审批通过后更新工序标准
6. 通知相关人员工序变更
7. 更新相关工艺路线

#### 2.3 工序版本管理流程
1. 创建工序新版本
2. 对比版本差异
3. 评估变更影响范围
4. 制定版本切换计划
5. 执行版本发布
6. 更新相关文档
7. 归档历史版本

### 后置条件
- 工序信息完整准确
- 工序标准已审核生效
- 相关工艺路线已更新
- 变更记录完整可追溯

## 3. 页面元素与交互说明 (Page Elements & Interactions)

### 页面/组件名称：工序管理页面
### 页面目标：提供工序主数据的创建、维护和查询功能

### 信息架构：
- **顶部区域**：包含 工序搜索, 新建工序, 批量操作, 导入导出
- **左侧区域**：包含 工序分类树, 工序类型筛选, 状态筛选
- **中间区域**：包含 工序列表, 工序详情, 编辑界面
- **右侧区域**：包含 工序统计, 关联信息, 操作历史

### 交互逻辑与状态：

#### **工序分类树**
- **默认状态：** 展示根分类，子分类折叠显示
- **展开状态：** 点击分类前的展开图标，显示子分类
- **选中状态：** 蓝色背景(#E6F7FF)，蓝色左边框
- **悬停状态：** 浅灰背景(#FAFAFA)，显示操作图标
- **交互行为：** 点击选中分类，中间区域显示对应工序列表

#### **工序列表**
- **列表项：**
  - **工序编码：** 显示唯一工序编码，点击进入详情
  - **工序名称：** 显示工序名称和简要描述
  - **工序类型：** 彩色标签显示工序类型
  - **标准工时：** 显示准备工时和单件工时
  - **状态标识：** 显示工序状态（草稿/生效/停用）
  - **最后更新：** 显示最后修改时间和修改人
- **排序功能：** 支持按编码、名称、类型、更新时间排序
- **筛选功能：** 支持按工序类型、状态、工艺分类筛选
- **搜索功能：** 支持工序编码、名称的模糊搜索

#### **工序详情/编辑界面**
- **基本信息：**
  - **工序编码：** 输入框，自动生成或手工输入，必填
  - **工序名称：** 输入框，必填，最大50字符
  - **工序描述：** 文本域，详细描述工序内容
  - **工序类型：** 下拉选择，切割/磨边/钢化/合片等
- **工艺参数：**
  - **准备工时：** 数字输入框，单位分钟
  - **单件工时：** 数字输入框，单位分钟
  - **拆卸工时：** 数字输入框，单位分钟
  - **工艺温度：** 数字输入框，适用于钢化等工序
- **质量要求：**
  - **质量标准：** 文本域，详细质量要求描述
  - **检验项目：** 多选框，关联质量检验项目
  - **合格标准：** 输入框，具体的合格判定标准
- **资源要求：**
  - **设备要求：** 多选框，选择适用设备类型
  - **技能要求：** 下拉选择，操作员技能等级要求
  - **人员数量：** 数字输入框，标准作业人员数量

#### **作业指导管理**
- **SOP文档：**
  - **操作步骤：** 富文本编辑器，详细操作步骤
  - **注意事项：** 文本域，操作注意事项
  - **安全规范：** 文本域，安全操作要求
  - **图片上传：** 支持上传操作示意图
- **文档版本：**
  - **版本号：** 显示当前版本号
  - **生效日期：** 日期选择器
  - **审核状态：** 显示审核状态和审核人
  - **历史版本：** 列表显示历史版本信息

### 数据校验规则：

#### **工序编码**
- **校验规则：** 编码必须全局唯一，符合编码规则格式
- **错误提示文案：** "工序编码已存在或格式不正确"

#### **工时配置**
- **校验规则：** 工时必须大于0，准备工时+单件工时不能为0
- **错误提示文案：** "工时配置不能为空且必须大于0"

#### **质量标准**
- **校验规则：** 关键工序必须配置质量标准
- **错误提示文案：** "关键工序必须配置质量检验标准"

## 4. 数据规格 (Data Requirements)

### 输入数据
- **工序基本信息**:
  - **工序编码 (operation_code)**: String, 必填, 全局唯一
  - **工序名称 (operation_name)**: String, 必填, 最大50字符
  - **工序类型 (operation_type)**: Enum, 必填, 切割/磨边/钢化/合片
  - **工序描述 (description)**: String, 可选, 最大500字符
- **工艺参数**:
  - **准备工时 (setup_time)**: Decimal, 必填, 单位分钟
  - **单件工时 (cycle_time)**: Decimal, 必填, 单位分钟
  - **拆卸工时 (teardown_time)**: Decimal, 可选, 单位分钟
- **质量要求**:
  - **质量标准 (quality_standard)**: String, 可选, 最大1000字符
  - **检验项目 (inspection_items)**: Array, 关联检验项目ID
  - **合格标准 (acceptance_criteria)**: String, 可选, 最大500字符

### 展示数据
- **工序列表**: 工序的基本信息和状态
- **工序详情**: 完整的工序标准信息
- **关联信息**: 工序在工艺路线中的使用情况
- **统计数据**: 工序使用频次和效率统计

### 空状态/零数据
- **无工序数据**: 显示"暂无工序数据，请先创建工序"
- **无关联信息**: 显示"该工序暂未被工艺路线使用"
- **无历史版本**: 显示"暂无历史版本记录"

### API接口
- **工序查询**: GET /api/pdm/operations
- **工序创建**: POST /api/pdm/operations
- **工序更新**: PUT /api/pdm/operations/{id}
- **工序删除**: DELETE /api/pdm/operations/{id}
- **版本管理**: GET/POST /api/pdm/operations/{id}/versions

## 5. 异常与边界处理 (Error & Edge Cases)

### **工序编码重复**
- **提示信息**: "工序编码已存在，请使用其他编码"
- **用户操作**: 编码字段标红，聚焦到编码输入框

### **删除被引用的工序**
- **提示信息**: "该工序正在被工艺路线使用，不能直接删除"
- **用户操作**: 显示引用详情，提供"停用工序"选项

### **工时配置异常**
- **提示信息**: "工时配置不合理，请检查工时设置"
- **用户操作**: 高亮异常字段，提供合理范围建议

### **版本冲突**
- **提示信息**: "检测到版本冲突，请刷新后重试"
- **用户操作**: 提供刷新按钮和冲突解决指导

### **权限不足**
- **提示信息**: "您没有权限修改此工序标准"
- **用户操作**: 显示只读模式，提供权限申请入口

## 6. 验收标准 (Acceptance Criteria)

- [ ] 支持工序的创建、编辑、查询、删除操作
- [ ] 工序编码全局唯一，支持自定义编码规则
- [ ] 完整的工序标准定义，包含工时、质量、资源要求
- [ ] 支持工序分类管理和层级结构
- [ ] 完善的版本管理和变更控制
- [ ] 与工艺路线设计模块的无缝集成
- [ ] 支持作业指导书的在线编辑和管理
- [ ] 提供工序使用统计和分析功能
- [ ] 数据准确性≥99%，系统可用性≥99.5%
- [ ] 页面响应时间<2秒，操作流畅
- [ ] 所有页面元素符合全局设计规范
- [ ] 支持工序标准的导入导出功能
